import { launch } from "puppeteer";
import { promises as fs } from 'fs';

// Helper function to convert array of arrays to a CSV string
const convertToCSV = (data) => {
    return data.map(row =>
        row.map(cell => {
            let cellText = String(cell);
            // Escape double quotes by doubling them
            cellText = cellText.replace(/"/g, '""');
            // If the cell contains a comma, a newline or a double quote, wrap it in double quotes
            if (/[",\n]/.test(cellText)) {
                cellText = `"${cellText}"`;
            }
            return cellText;
        }).join(',')
    ).join('\n');
};

async function main() {
  const inputFile = 'cpfs.csv';
  const outputFile = 'resultado_final.csv';
  let cpfsToProcess;

  // Read the list of CPFs from the input file
  try {
    const fileContent = await fs.readFile(inputFile, 'utf8');
    // Filter out empty lines and ensure they are valid-looking numbers
    cpfsToProcess = fileContent.split(/\r?\n/).filter(line => line.trim() && /^\d+$/.test(line.trim()));
    if (cpfsToProcess.length === 0) {
        console.log(`No valid CPFs found in '${inputFile}'. Please add CPFs, one per line.`);
        return;
    }
    console.log(`Found ${cpfsToProcess.length} CPFs to process from '${inputFile}'.`);
  } catch (error) {
    console.error(`Error: Could not read input file '${inputFile}'.`);
    console.log(`Please create a file named '${inputFile}' in the same directory with one CPF per line.`);
    return;
  }

  const browser = await launch({
    headless: false, // Set to true for headless mode
    defaultViewport: null,
  });

  try {
    const page = await browser.newPage();
    // It's good practice to have a longer default timeout for waiting on elements
    page.setDefaultTimeout(60000);

    // Step 1: Navigate to login page
    console.log("Navigating to login page...");
    await page.goto("https://funcaoconsig.digio.com.br/WebAutorizador/Login/");

    // Step 2: Wait for manual login.
    // We wait for an element that only appears AFTER login.
    // Setting timeout to 0 means it will wait indefinitely for you to log in.
    console.log("Waiting for you to log in manually...");
    await page.waitForSelector('a.dropdown-toggle[data-toggle="dropdown"]', {
      timeout: 0,
    });
    console.log("Login detected, proceeding...");

    // Execute steps 3-5 once before the loop
    // Step 3: Hover over "Cadastro" dropdown to open menu
    console.log("Hovering over Cadastro dropdown...");
    await page.hover('a.dropdown-toggle[data-toggle="dropdown"]');

    // Step 4: Click "Proposta Consignado"
    // Instead of a fixed wait, we wait for the menu item to be clickable.
    console.log("Clicking Proposta Consignado...");
    await page.waitForSelector("#WFP2010_PWCDPRPS", { visible: true });
    await page.click("#WFP2010_PWCDPRPS");

    // Step 5: Wait for page load and click "Confirmar"
    console.log("Waiting for page load and clicking Confirmar...");
    await page.waitForSelector("#btnConfirmar_txt", { visible: true });
    await page.click("#btnConfirmar_txt");

    // Step 6: Select "REFINANCIAMENTO" option
    console.log("Selecting REFINANCIAMENTO...");
    // Wait for the first dropdown to be ready and select the option.
    await page.waitForSelector('select > option[value="Refinanciamento"]');
    await page.select('select', 'Refinanciamento');

    // Step 7: Wait for load and select "INSS"
    console.log("Selecting INSS...");

    await page.waitForSelector('select > option[value="5"]'); // Wait for the option to be populated
    const selectHandles = await page.$$("select");
    if (selectHandles.length > 1) {
      await selectHandles[1].select("5"); // Select 'INSS' from the second dropdown
    } else {
      throw new Error("Could not find the second dropdown to select 'INSS'.");
    }

    // Step 8: Click radio button
    console.log("Clicking radio button...");
    const radioButtonSelector =
      "#ctl00_Cph_UcPrp_FIJN1_JnDadosIniciais_UcDIni_UcTipoFormalizacao_rblTpFormalizacao_1";
    await page.waitForSelector(radioButtonSelector, { visible: true });
    await page.click(radioButtonSelector);

    // This flag will help us write the header only once.
    let isHeaderWritten = await fs.access(outputFile).then(() => true).catch(() => false);

    for (const currentCpf of cpfsToProcess) {
      try {
        console.log(`\n--- Processing CPF: ${currentCpf} ---`);

    // Step 9: Input the current CPF from the list
    console.log(`Typing CPF: ${currentCpf}...`);
    const cpfInputSelector =
      "#ctl00_Cph_UcPrp_FIJN1_JnDadosIniciais_UcDIni_txtCPF_CAMPO";
    await page.waitForSelector(cpfInputSelector, { visible: true });

    // A more robust way to fill input fields on complex pages.
    await page.click(cpfInputSelector, { clickCount: 3 });
    await page.keyboard.press('Backspace');
    await page.type(cpfInputSelector, currentCpf);
    await page.keyboard.press('Tab');
    
    // Step 10: Wait for the iframe and click the link inside
    console.log("Waiting for iframe to appear...");
    // Wait for an iframe to be visible. If there are multiple, you may need a more specific selector.
    const frameElementHandle = await page.waitForSelector('iframe', { visible: true });
    const frame = await frameElementHandle.contentFrame();

    if (!frame) {
      throw new Error("Could not access the iframe's content.");
    }

    // Now, interact with elements inside the frame using the `frame` object
    console.log("Frame found. Clicking the link inside...");
    const linkSelector = '#ctl00_cph_FIJanela1_FIJanelaPanel1_grvHomo_ctl02_lnkCodigo';
    await frame.waitForSelector(linkSelector, { visible: true });
    await frame.click(linkSelector);

    // After this, any `page.` calls will be back on the main page context automatically.
    // The iMacros `FRAME F=0` is not needed.
    console.log("Clicked link inside the frame.");

    // It's very common for a click to trigger background network activity (like fetching data).
    // Before proceeding, let's wait for the network to be idle, which is a good sign the page has updated and is ready.
    await page.waitForNetworkIdle({ idleTime: 500 });

    // Step 10.5: Extract personal data to be used later
    console.log("Extracting personal data...");
    const nomeSelector = '#ctl00_Cph_UcPrp_FIJN1_FIJanelaPanel1_UcDadosPessoaisClienteSnt_txtNome_CAMPO';
    const dddSelector = '#ctl00_Cph_UcPrp_FIJN1_FIJanelaPanel1_UcDadosPessoaisClienteSnt_txtDddTelCelular_CAMPO';
    const telSelector = '#ctl00_Cph_UcPrp_FIJN1_FIJanelaPanel1_UcDadosPessoaisClienteSnt_txtTelCelular_CAMPO';
    const cpfSelector = '#ctl00_Cph_UcPrp_FIJN1_JnDadosIniciais_UcDIni_txtCPF_CAMPO';
    const nascSelector = '#ctl00_Cph_UcPrp_FIJN1_JnDadosIniciais_UcDIni_txtDataNascimento_CAMPO';

    // Wait for one of the fields to be populated to ensure data is loaded.
    await page.waitForFunction((selector) => document.querySelector(selector)?.value.length > 0, {}, nomeSelector);

    const nome = await page.$eval(nomeSelector, el => el.value);
    const ddd = await page.$eval(dddSelector, el => el.value);
    const telefone = await page.$eval(telSelector, el => el.value);
    const cpf = await page.$eval(cpfSelector, el => el.value);
    const nascimento = await page.$eval(nascSelector, el => el.value);

    const personalHeaders = ['Nome', 'CPF', 'DataNascimento', 'Telefone'];
    const personalValues = [nome, cpf, nascimento, `(${ddd}) ${telefone}`];
    console.log("Personal data extracted.");

    // Step 11: Click 'Atualizar Lista de Contratos' button
    console.log("Clicking 'Atualizar Lista de Contratos' button...");
    const atualizarButtonSelector = '#btAtuListaContratos_txt';
    await page.waitForSelector(atualizarButtonSelector, { visible: true });
    // On some complex web applications, a standard click doesn't always work.
    // Using page.evaluate() to trigger the click directly in the browser context is more reliable.
    await page.evaluate((selector) => {
        document.querySelector(selector)?.click();
    }, atualizarButtonSelector);
    console.log("Clicked 'Atualizar Lista de Contratos' button.");

    // Step 12: Extract table data and save to CSV
    console.log("Waiting for the contracts table to appear...");
    const tableSelector = '#ctl00_Cph_UcPrp_FIJN1_JnRefinReneg_UcRefin_grdOperacoes';
    await page.waitForSelector(tableSelector, { visible: true });
    console.log("Table found. Extracting data...");

    // This function will be executed in the browser context to scrape the table
    const tableData = await page.$eval(tableSelector, table => {
        const data = [];
        const rows = Array.from(table.querySelectorAll('tr'));

        // The first row is the header
        const headerRow = rows.shift();
        if (headerRow) {
            const headerCols = Array.from(headerRow.querySelectorAll('th'));
            const headers = headerCols.map(th => th.innerText.trim());
            // The first column is a checkbox, let's name the header accordingly.
            headers[0] = 'Selecionado';
            data.push(headers);
        }

        // Process data rows
        for (const row of rows) {
            const columns = Array.from(row.querySelectorAll('td'));
            const rowData = columns.map((col, index) => {
                // First column is the checkbox
                if (index === 0) {
                    const checkbox = col.querySelector('input[type="checkbox"]');
                    return checkbox ? (checkbox.checked ? 'SIM' : 'NÃO') : '';
                }
                // For other columns, just get the text
                return col.innerText.trim();
            });
            data.push(rowData);
        }
        return data;
    });

        // Step 13: Combine personal and contract data and save incrementally
        console.log("Combining and saving data for the current CPF...");

        const dataToSave = [];
        const contractHeaders = tableData.length > 0 ? tableData[0] : [];

        if (!isHeaderWritten) {
            const combinedHeaders = [...personalHeaders, ...contractHeaders];
            dataToSave.push(combinedHeaders);
        }

        if (tableData.length <= 1) {
            console.log("No contract data found, saving personal data only.");
            const paddedRow = [...personalValues, ...Array(contractHeaders.length).fill('')];
            dataToSave.push(paddedRow);
        } else {
            for (let i = 1; i < tableData.length; i++) {
                const contractRow = tableData[i];
                const combinedRow = [...personalValues, ...contractRow];
                dataToSave.push(combinedRow);
            }
        }

        if (dataToSave.length > 0) {
            const csvContent = convertToCSV(dataToSave);
            if (!isHeaderWritten) {
                await fs.writeFile(outputFile, csvContent, 'utf8');
                console.log(`Data successfully saved to new file ${outputFile}`);
                isHeaderWritten = true;
            } else {
                // Add a newline before appending to an existing file
                await fs.appendFile(outputFile, '\n' + csvContent, 'utf8');
                console.log(`Data successfully appended to ${outputFile}`);
            }
        }

        console.log(`Successfully processed CPF: ${currentCpf}`);

      } catch (e) {
        console.error(`\n[ERROR] Failed to process CPF ${currentCpf}: ${e.message}`);
        await fs.appendFile('errors.log', `${new Date().toISOString()}: Failed CPF ${currentCpf} - ${e.stack}\n`, 'utf8');
        console.log("Logged error to errors.log. Continuing to the next CPF...");
      }
    } // End of for...of loop

    console.log("\nAll CPFs processed. Scraper finished.");
  } catch (error) {
    console.error("Error during scraping:", error);
  } finally {
    console.log("Closing browser...");
    await browser.close();
  }
}

main();
