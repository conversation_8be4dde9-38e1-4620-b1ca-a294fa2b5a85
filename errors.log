2025-07-23T17:00:40.478Z: Failed CPF 02544984422 - Error: No element found for selector: a.dropdown-toggle[data-toggle="dropdown"]
    at assert (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/util/assert.js:15:15)
    at CdpFrame.hover (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/api/Frame.js:796:17)
    at async main (file:///C:/Users/<USER>/codes/digiobot/scraper.js:107:5)
2025-07-23T17:02:42.484Z: Failed CPF 02544984422 - TargetCloseError: Waiting for selector `a.dropdown-toggle[data-toggle="dropdown"]` failed: Protocol error (Runtime.callFunctionOn): Target closed
    at CallbackRegistry.clear (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/common/CallbackRegistry.js:78:36)
    at CdpCDPSession.onClosed (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/cdp/CdpSession.js:111:25)
    at Connection.onMessage (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/cdp/Connection.js:136:25)
    at WebSocket.<anonymous> (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/node/NodeWebSocketTransport.js:37:32)
    at callListener (C:\Users\<USER>\codes\digiobot\node_modules\ws\lib\event-target.js:290:14)
    at WebSocket.onMessage (C:\Users\<USER>\codes\digiobot\node_modules\ws\lib\event-target.js:209:9)
    at WebSocket.emit (node:events:507:28)
    at Receiver.receiverOnMessage (C:\Users\<USER>\codes\digiobot\node_modules\ws\lib\websocket.js:1220:20)
    at Receiver.emit (node:events:507:28)
    at Immediate.<anonymous> (C:\Users\<USER>\codes\digiobot\node_modules\ws\lib\receiver.js:601:16)
2025-07-23T17:06:38.183Z: Failed CPF 02544984422 - TargetCloseError: Waiting for selector `a.dropdown-toggle[data-toggle="dropdown"]` failed: Protocol error (Runtime.callFunctionOn): Target closed
    at CallbackRegistry.clear (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/common/CallbackRegistry.js:78:36)
    at CdpCDPSession.onClosed (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/cdp/CdpSession.js:111:25)
    at Connection.onMessage (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/cdp/Connection.js:136:25)
    at WebSocket.<anonymous> (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/node/NodeWebSocketTransport.js:37:32)
    at callListener (C:\Users\<USER>\codes\digiobot\node_modules\ws\lib\event-target.js:290:14)
    at WebSocket.onMessage (C:\Users\<USER>\codes\digiobot\node_modules\ws\lib\event-target.js:209:9)
    at WebSocket.emit (node:events:507:28)
    at Receiver.receiverOnMessage (C:\Users\<USER>\codes\digiobot\node_modules\ws\lib\websocket.js:1220:20)
    at Receiver.emit (node:events:507:28)
    at Immediate.<anonymous> (C:\Users\<USER>\codes\digiobot\node_modules\ws\lib\receiver.js:601:16)
