2025-07-23T17:11:36.636Z: Failed CPF 02544984422 - TargetCloseError: Protocol error (Runtime.callFunctionOn): Target closed
    at CallbackRegistry.clear (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/common/CallbackRegistry.js:78:36)
    at CdpCDPSession.onClosed (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/cdp/CdpSession.js:111:25)
    at Connection.onMessage (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/cdp/Connection.js:136:25)
    at WebSocket.<anonymous> (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/node/NodeWebSocketTransport.js:37:32)
    at callListener (C:\Users\<USER>\codes\digiobot\node_modules\ws\lib\event-target.js:290:14)
    at WebSocket.onMessage (C:\Users\<USER>\codes\digiobot\node_modules\ws\lib\event-target.js:209:9)
    at WebSocket.emit (node:events:507:28)
    at Receiver.receiverOnMessage (C:\Users\<USER>\codes\digiobot\node_modules\ws\lib\websocket.js:1220:20)
    at Receiver.emit (node:events:507:28)
    at Immediate.<anonymous> (C:\Users\<USER>\codes\digiobot\node_modules\ws\lib\receiver.js:601:16)
2025-07-23T17:11:36.733Z: Failed CPF 09135219572 - Error: Waiting for selector `#ctl00_Cph_UcPrp_FIJN1_JnDadosIniciais_UcDIni_txtCPF_CAMPO` failed: waitForFunction failed: frame got detached.
    at IsolatedWorld.dispose (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/api/Realm.js:37:39)
    at [Symbol.dispose] (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/api/Realm.js:41:14)
    at [Symbol.dispose] (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/cdp/IsolatedWorld.js:153:29)
    at [Symbol.dispose] (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/cdp/Frame.js:327:56)
    at #removeFramesRecursively (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/cdp/FrameManager.js:449:29)
    at #onClientDisconnect (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/cdp/FrameManager.js:96:42)
2025-07-23T17:11:36.740Z: Failed CPF 53989279904 - Error: Attempted to use detached Frame 'EED85F3B4638AA4106C05FA84862D226'.
    at CdpFrame.<anonymous> (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/util/decorators.js:99:23)
    at CdpPage.waitForSelector (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:1378:43)
    at main (file:///C:/Users/<USER>/codes/digiobot/scraper.js:115:16)
2025-07-23T17:11:36.750Z: Failed CPF 12224871104 - Error: Attempted to use detached Frame 'EED85F3B4638AA4106C05FA84862D226'.
    at CdpFrame.<anonymous> (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/util/decorators.js:99:23)
    at CdpPage.waitForSelector (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:1378:43)
    at main (file:///C:/Users/<USER>/codes/digiobot/scraper.js:115:16)
2025-07-23T17:11:36.755Z: Failed CPF 86855611320 - Error: Attempted to use detached Frame 'EED85F3B4638AA4106C05FA84862D226'.
    at CdpFrame.<anonymous> (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/util/decorators.js:99:23)
    at CdpPage.waitForSelector (file:///C:/Users/<USER>/codes/digiobot/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:1378:43)
    at main (file:///C:/Users/<USER>/codes/digiobot/scraper.js:115:16)
